{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "push": "../pushScript.sh", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "eslint": "^9.24.0", "firebase-functions-test": "^3.4.1", "globals": "^16.0.0"}, "private": true, "type": "module"}