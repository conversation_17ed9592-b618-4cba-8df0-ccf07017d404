import createSession from "./services/filevine/createSession.js"
import createSessionPAT from "./services/filevine/createSessionPAT.js"
import getForm from "./services/filevine/getForm.js"
import getProject from "./services/filevine/getProject.js"
import getUser from "./services/filevine/getUser.js"
import updateProject from "./services/filevine/updateProject.js"
import createNote from "./services/filevine/createNote.js"
import createCollectionItem from "./services/filevine/createCollectionItem.js"
import getCollectionItem from "./services/filevine/getCollectionItem.js"

import { getFirestore } from "firebase-admin/firestore"
import { FieldValue } from "firebase-admin/firestore"

export const automation = async (req, res) => {
  const db = getFirestore()
  console.log(`${req.params[0]} - ${req.body.Object} ${req.body.Event}`)
  console.log(req.body)

  const tenant = req.params[0]
  const eventBody = req.body

  let creds
  let accessToken
  let refreshToken

  let tenantAutomationStatus

  await db
    .collection(tenant)
    .doc("automations")
    .get()
    .then((data) => (tenantAutomationStatus = data.data()))
    .catch((err) => console.log(err, "error getting tenant automation status"))

  // console.log(tenantAutomationStatus, "tenant automation status")

  if (tenantAutomationStatus.enabled) {
    if (eventBody.Event === "PhaseChanged" && tenantAutomationStatus.logPhaseChangeEnabled) {
      await db
        .collection(tenant)
        .doc("fv-creds")
        .get()
        .then((data) => (creds = data.data()))
        .catch((err) => console.log(err, "error getting tenant automation status"))

      // =====<>=====<>=====<>=====<>=====<>=====<>=====<>=====<>=====<>
      // LOG PHASE CHANGE

      let projectData

      await db
        .collection(tenant)
        .doc("project-data")
        .get()
        .then((data) => (projectData = data.data()[eventBody.ProjectId]))
        .catch((err) => console.log(err, "error getting project data"))

      console.log(projectData, "project data")

      if (projectData) {
        const phaseChangedObject = {
          fromPhaseName: projectData.currentPhase,
          fromPhaseId: projectData.phaseId,
          fromTimestamp: projectData.lastPhaseChangeTimestamp,
          toPhaseName: eventBody.Other.PhaseName,
          toPhaseId: eventBody.ObjectId.PhaseId,
          toTimestamp: eventBody.Timestamp,
          userId: eventBody.UserId
        }

        await db
          .collection(tenant)
          .doc("project-data")
          .collection(`${eventBody.ProjectId}`)
          .doc("phase-data")
          .set(
            {
              allPhaseChanges: FieldValue.arrayUnion(phaseChangedObject)
            },
            { merge: true }
          )
          .then(() =>
            console.log(`project ${eventBody.ProjectId} phase changed to ${eventBody.Other.PhaseName}`)
          )
          .catch((err) => console.log(err, "error getting project data"))

        let timeDifference = eventBody.Timestamp - projectData.lastPhaseChangeTimestamp
        timeDifference = (timeDifference / 86400000) * 10
        timeDifference = Math.ceil(timeDifference) / 10

        await db
          .collection(tenant)
          .doc("kanban")
          .collection(String(eventBody.ObjectId.ProjectTypeId))
          .doc("phase-averages")
          .set(
            {
              [projectData.phaseId]: FieldValue.arrayUnion(timeDifference)
            },
            { merge: true }
          )
          .then(() =>
            console.log(`project ${eventBody.ProjectId} phase changed to ${eventBody.Other.PhaseName}`)
          )
          .catch((err) => console.log(err, "error getting project data"))
      }

      await db
        .collection(tenant)
        .doc("project-data")
        .set(
          {
            [eventBody.ProjectId]: {
              lastPhaseChangeTimestamp: eventBody.Timestamp,
              currentPhase: eventBody.Other.PhaseName,
              phaseId: eventBody.ObjectId.PhaseId,
              userId: eventBody.UserId
            }
          },
          { merge: true }
        )
        .then(() =>
          console.log(`project ${eventBody.ProjectId} phase changed to ${eventBody.Other.PhaseName}`)
        )
        .catch((err) => console.log(err, "error getting project data"))

      // =====<>=====<>=====<>=====<>=====<>=====<>=====<>=====<>=====<>==

      if (
        eventBody.UserId.toString() !== creds.apiUserId &&
        eventBody.UserId.toString() !== creds.serviceAcctUserId
      ) {
        if (creds.pat) {
          await createSessionPAT(creds).then((data) => {
            accessToken = data.access_token
          })
        } else {
          await createSession(creds)
            .then((data) => {
              accessToken = data.accessToken
              refreshToken = data.refreshToken
            })
            .catch((err) => console.log(err, "error"))
        }

        let triggerUserFullname

        await getUser(accessToken, refreshToken, creds, eventBody.UserId)
          .then((data) => (triggerUserFullname = `${data.user.firstName} ${data.user.lastName}`))
          .catch((err) => console.log(err, "error getting user"))

        const noteBody = {
          body: `_${triggerUserFullname}_ has changed the project phase to _${eventBody.Other.PhaseName}_`,
          projectId: {
            native: eventBody.ProjectId
          }
        }

        await createNote(accessToken, refreshToken, creds, noteBody)
          .then(() => console.log("note created"))
          .catch(() => console.log("error creating note"))
      }
    }

    if (eventBody.Object === "Project" && eventBody.Event === "Created") {
      // LOG INITIAL PHASE WHEN PROJECT CREATED =============================================
      await db
        .collection(tenant)
        .doc("fv-creds")
        .get()
        .then((data) => (creds = data.data()))
        .catch((err) => console.log(err, "error getting tenant automation status"))

      if (creds.pat) {
        await createSessionPAT(creds).then((data) => {
          accessToken = data.access_token
        })
      } else {
        await createSession(creds)
          .then((data) => {
            accessToken = data.accessToken
            refreshToken = data.refreshToken
          })
          .catch((err) => console.log(err, "error"))
      }

      let projectData

      await getProject(accessToken, refreshToken, creds, eventBody.ProjectId)
        .then((data) => (projectData = data))
        .catch((err) => console.log(err, "error getting current project name"))

      await db
        .collection(tenant)
        .doc("project-data")
        .set(
          {
            [eventBody.ProjectId]: {
              lastPhaseChangeTimestamp: eventBody.Timestamp,
              currentPhase: projectData.phaseName,
              phaseId: eventBody.Other.PhaseId
            }
          },
          { merge: true }
        )
        .then(() => console.log(`project ${eventBody.ProjectId} phase set to ${projectData.phaseName}`))
        .catch((err) => console.log(err, "error getting project data"))

      //=====================================================================================

      if (tenantAutomationStatus.projectNumberIncrementorEnabled) {
        let assignedValue = tenantAutomationStatus.incrementorData.nextAssignedValue

        const projectUpdateBody = {
          number: assignedValue
        }

        if (tenantAutomationStatus.incrementorData.concatenateTitle) {
          projectUpdateBody.projectName = `${projectData.projectName} | ${assignedValue}`
        }

        console.log(projectUpdateBody, "pj update body")

        // (POST NEW PROJECT NUMBER)
        await updateProject(accessToken, refreshToken, creds, eventBody.ProjectId, projectUpdateBody)
          .then((data) => console.log(data, "project number updated"))
          .catch(() => console.log("error updating project number"))

        let number = assignedValue.match(/\d+/) === null ? 0 : assignedValue.match(/\d+/)[0]
        let numberLength = number.length
        number = (parseInt(number) + 1).toString()
        while (number.length < numberLength) {
          number = "0" + number
        }
        let nextAssignedValue = assignedValue.replace(/[0-9]/g, "").concat(number)

        await db
          .collection(tenant)
          .doc("automations")
          .update({
            incrementorData: {
              concatenateTitle: tenantAutomationStatus.incrementorData.concatenateTitle,
              nextAssignedValue: nextAssignedValue
            }
          })
          .then(() => console.log("automations enabled"))
          .catch(() => console.log("error enabling automations"))
      }
    }

    if (eventBody.Object === "CollectionItem" && eventBody.Event === "Created") {
      if (tenantAutomationStatus.createCollectionNote) {
        await db
          .collection(tenant)
          .doc("fv-creds")
          .get()
          .then((data) => (creds = data.data()))
          .catch((err) => console.log(err, "error getting tenant automation status"))

        if (
          eventBody.UserId.toString() !== creds.apiUserId &&
          eventBody.UserId.toString() !== creds.serviceAcctUserId
        ) {
          if (creds.pat) {
            await createSessionPAT(creds).then((data) => {
              accessToken = data.access_token
            })
          } else {
            await createSession(creds)
              .then((data) => {
                accessToken = data.accessToken
                refreshToken = data.refreshToken
              })
              .catch((err) => console.log(err, "error"))
          }

          let triggerUserFullname

          await getUser(accessToken, refreshToken, creds, eventBody.UserId)
            .then((data) => (triggerUserFullname = `${data.user.firstName} ${data.user.lastName}`))
            .catch((err) => console.log(err, "error getting user"))

          const noteBody = {
            body: `_${triggerUserFullname}_ has created a new _${eventBody.ObjectId.SectionSelector}_ item`,
            projectId: {
              native: eventBody.ProjectId
            }
          }

          await createNote(accessToken, refreshToken, creds, noteBody).then(() => {
            console.log("success creating new collection note")
          })
        }
      }
    }

    if (eventBody.Object === "Taskflow" && eventBody.Event === "Executed") {
      let triggerKey = `${eventBody.ObjectId.ProjectTypeId}-${eventBody.ObjectId.FieldSelector}-${eventBody.ObjectId.SectionSelector}`
      let createCollectionAutomationData

      await db
        .collection(tenant)
        .doc("automations")
        .collection("customTaskflowAutomations")
        .doc(triggerKey)
        .get()
        .then((data) => (createCollectionAutomationData = data.data()))
        .catch((err) => console.log(err, "error getting tenant automation status"))

      if (!createCollectionAutomationData) {
        res.status(200).send("Thank you!")
        return
      }

      // console.log(createCollectionAutomationData, 'create collection data')
      // return

      for (let targetSelector in createCollectionAutomationData.createCollectionAutomations) {
        let automation = createCollectionAutomationData.createCollectionAutomations[targetSelector]

        if (automation.enabled) {
          if (!creds && !accessToken && !refreshToken) {
            await db
              .collection(tenant)
              .doc("fv-creds")
              .get()
              .then((data) => (creds = data.data()))
              .catch((err) => console.log(err, "error getting tenant automation status"))

            if (creds.pat) {
              await createSessionPAT(creds).then((data) => {
                accessToken = data.access_token
              })
            } else {
              await createSession(creds)
                .then((data) => {
                  accessToken = data.accessToken
                  refreshToken = data.refreshToken
                })
                .catch((err) => console.log(err, "error"))
            }
          }

          let triggerSection

          if (!eventBody.Other.ItemId || eventBody.Other.ItemId === "00000000-0000-0000-0000-000000000000") {
            await getForm(
              accessToken,
              refreshToken,
              creds,
              eventBody.ProjectId,
              automation.triggerSectionSelector
            ).then((data) => (triggerSection = data))
          } else {
            await getCollectionItem(
              accessToken,
              refreshToken,
              creds,
              eventBody.ProjectId,
              automation.triggerSectionSelector,
              eventBody.Other.ItemId
            ).then((data) => {
              console.log(data, "collection data")
              triggerSection = data.dataObject
            })
          }

          let collectionBody = {
            dataObject: {}
          }

          for (let triggerField in automation.fieldMap) {
            const targetField = automation.fieldMap[triggerField]
            if (targetField) {
              collectionBody.dataObject[targetField] = triggerSection[triggerField]
            }
          }

          console.log(collectionBody, "request collection body")

          await createCollectionItem(
            accessToken,
            refreshToken,
            creds,
            eventBody.ProjectId,
            automation.targetSectionSelector,
            collectionBody
          ).then((data) => console.log(data, "response collection body"))
        } else {
          console.log("not enabled")
        }
      }
    }
    res.status(200).send("Thank you!")
    return
  }
  res.status(200).send("Thank you!")
  return
}
