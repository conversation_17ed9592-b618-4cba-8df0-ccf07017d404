import { getFirestore } from "firebase-admin/firestore"

const getPhaseOrder = async (tenant, projectTypeId) => {
  const db = getFirestore()
  const phaseList = db.collection(tenant).doc("kanban").collection(projectTypeId).doc("phase-order")
  const phaseDoc = await phaseList.get()

  if (!phaseDoc.exists) {
    throw new Error("No phase order found for project type")
  }

  return phaseDoc.data()
}

export default getPhaseOrder
