import { NEW_BASE_URL } from "../../constants/constants.js"

const createCollectionItem = async (accessToken, refreshToken, creds, projectId, sectionSelector, body) => {
  let baseUrl = ""
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  if (creds.pat) {
    baseUrl = NEW_BASE_URL
    headersObj["x-fv-orgid"] = creds.orgId
    headersObj["x-fv-userid"] = creds.serviceAcctUserId
  } else {
    baseUrl = `https://${creds.tenant}.api.${creds.type}.com/core`
    headersObj["x-fv-sessionid"] = refreshToken
  }

  let response = await fetch(`${baseUrl}/projects/${projectId}/collections/${sectionSelector}`, {
    method: "POST",
    mode: "cors",
    headers: headersObj,
    body: JSON.stringify(body)
  })

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | Error at createCollectionItem`)
  } else {
    return response.json()
  }
}

export default createCollectionItem
