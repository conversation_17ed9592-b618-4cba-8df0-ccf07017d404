import { NEW_BASE_URL } from "../../constants/constants.js"

const getProjectList = async (accessToken, refreshToken, creds, limit, offset) => {
  let baseUrl = ""
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  if (creds.pat) {
    baseUrl = NEW_BASE_URL
    headersObj["x-fv-orgid"] = creds.orgId
    headersObj["x-fv-userid"] = creds.serviceAcctUserId
  } else {
    baseUrl = `https://${creds.tenant}.api.${creds.type}.com/core`
    headersObj["x-fv-sessionid"] = refreshToken
  }

  let response = await fetch(
    `${baseUrl}/projects?limit=${limit}&offset=${offset}&excludeArchived=true&requestedFields=phaseName,clientName,firstPrimaryUsername,firstPrimaryName,hashtags,projectEmailAddress,createdDate,projectId,projectTypeId,projectName,phaseId,number,projectUrl`,
    {
      method: "GET",
      mode: "cors",
      headers: headersObj
    }
  )

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | Error at getProjectList`)
  } else {
    return response.json()
  }
}

export default getProjectList
