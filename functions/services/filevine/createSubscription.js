const createSubscription = async (accessToken, refreshToken, creds, body) => {
  let baseUrl = ""
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  if (creds.pat) {
    baseUrl = "https://api.filevineapp.com/fv-app/v2/webhooks/subscription"
    headersObj["x-fv-orgid"] = creds.orgId
    headersObj["x-fv-userid"] = creds.serviceAcctUserId
  } else {
    baseUrl = `https://${creds.tenant}.api.${creds.type}.com/subscriptions`
    headersObj["x-fv-sessionid"] = refreshToken
  }

  let response = await fetch(baseUrl, {
    method: "POST",
    mode: "cors",
    headers: headersObj,
    body: JSON.stringify(body)
  })

  if (response.status >= 300) {
    console.error(`${response.status} ${response.statusText} | Error at createSubscription`)
  } else {
    return response.json()
  }
}

export default createSubscription
