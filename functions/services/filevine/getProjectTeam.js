import { NEW_BASE_URL } from "../../constants/constants.js"

const getProjectTeam = async (accessToken, refreshToken, creds, projectId) => {
  let baseUrl = ""
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  if (creds.pat) {
    baseUrl = NEW_BASE_URL
    headersObj["x-fv-orgid"] = creds.orgId
    headersObj["x-fv-userid"] = creds.serviceAcctUserId
  } else {
    baseUrl = `https://${creds.tenant}.api.${creds.type}.com/core`
    headersObj["x-fv-sessionid"] = refreshToken
  }

  let response = await fetch(
    `${baseUrl}/projects/${projectId}/team?limit=1000&requestedFields=userId,fullname,username`,
    {
      method: "GET",
      mode: "cors",
      headers: headersObj
    }
  )

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | getProjectTeam`)
  } else {
    return response.json()
  }
}

export default getProjectTeam
