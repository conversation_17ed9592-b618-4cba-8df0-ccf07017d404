/**
 * Helper methods for managing webhook subscription data in Firestore
 *
 * These utilities handle storing and retrieving subscription IDs and related data
 * in the history-tracker document for each tenant.
 */

import { getFirestore } from "firebase-admin/firestore"

/**
 * Stores a subscription ID in the history-tracker document
 *
 * @param {string} tenant - The tenant identifier
 * @param {string} subscriptionId - The subscription ID to store
 * @returns {Promise<void>}
 */
const storeSubscriptionId = async (tenant, subscriptionId) => {
  const db = getFirestore()
  try {
    await db.collection(tenant).doc("history-tracker").set(
      {
        formChangeSubscriptionId: subscriptionId,
        updatedAt: new Date().toISOString()
      },
      { merge: true }
    )
  } catch (error) {
    console.error("Error storing subscription ID:", error)
    throw new Error(`Failed to store subscription ID: ${error.message}`)
  }
}

/**
 * Retrieves a subscription ID from the history-tracker document
 *
 * @param {string} tenant - The tenant identifier
 * @returns {Promise<string|null>} - The subscription ID or null if not found
 */
const getStoredSubscriptionId = async (tenant) => {
  const db = getFirestore()

  try {
    const doc = await db.collection(tenant).doc("history-tracker").get()

    if (!doc.exists) {
      return null
    }

    const data = doc.data()
    const subscriptionId = data?.formChangeSubscriptionId || null
    return subscriptionId
  } catch (error) {
    console.error("Error retrieving subscription ID:", error)
    throw new Error(`Failed to retrieve subscription ID: ${error.message}`)
  }
}

/**
 * Removes the subscription ID from the history-tracker document
 *
 * @param {string} tenant - The tenant identifier
 * @returns {Promise<void>}
 */
const removeStoredSubscriptionId = async (tenant) => {
  const db = getFirestore()

  try {
    await db.collection(tenant).doc("history-tracker").update({
      formChangeSubscriptionId: null,
      updatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error removing subscription ID:", error)
    throw new Error(`Failed to remove subscription ID: ${error.message}`)
  }
}

export { storeSubscriptionId, getStoredSubscriptionId, removeStoredSubscriptionId }
