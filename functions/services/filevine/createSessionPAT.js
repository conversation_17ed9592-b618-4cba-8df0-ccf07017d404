export const createSessionPAT = async (creds) => {
  let response = await fetch(`https://identity.filevine.com/connect/token`, {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    body: new URLSearchParams({
      client_id: "b273c30d-9074-4e33-bb12-5bbc2d481c6f",
      client_secret: "CewdPo^4j7IzoWwP$BI{nuU7_",
      grant_type: "personal_access_token",
      scope:
        "email filevine.v2.api.* filevine.v2.webhooks fv.api.gateway.access fv.auth.tenant.read fv.vitals.api.* openid tenant",
      token: creds.pat
    })
  })

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | Error at createSessionPAT`)
  } else {
    return response.json()
  }
}

export default createSessionPAT
