import crypto from "crypto"

const createSession = async (creds) => {
  const timestamp = new Date().toISOString()
  const data = [creds.key, timestamp, creds.secret].join("/")
  let hash = crypto.createHash("md5").update(data).digest("hex")

  let response = await fetch(`https://${creds.tenant}.api.${creds.type}.com/session`, {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      mode: "key",
      apiKey: creds.key,
      apiHash: hash,
      apiTimestamp: timestamp
    })
  })

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | Error at createSession`)
  } else {
    return response.json()
  }
}

export default createSession
