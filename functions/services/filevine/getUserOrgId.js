const getUserOrgId = async (accessToken) => {
  const headersObj = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${accessToken}`
  }

  let response = await fetch(`https://api.filevineapp.com/fv-app/v2/utils/GetUserOrgsWithToken`, {
    method: "POST",
    mode: "cors",
    headers: headersObj
  })

  if (response.status !== 200) {
    console.error(`${response.status} ${response.statusText} | Error at getUserOrgId`)
  } else {
    return response.json()
  }
}

export default getUserOrgId
