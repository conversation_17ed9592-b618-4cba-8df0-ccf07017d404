import { getFirestore } from "firebase-admin/firestore"

const getCredsFromFirestore = async (emailAddress) => {
  const db = getFirestore()
  let userData

  const userDoc = await db.collection("users").doc(emailAddress).get()

  if (!userDoc.exists) {
    throw new Error("User not found in database with provided email address")
  }

  userData = userDoc.data()

  const fvAuthdoc = await db.collection(userData.tenant).doc("fv-creds").get()

  if (!fvAuthdoc.exists) {
    throw new Error("No Filevine credentials found for tenant")
  }

  const fvCreds = fvAuthdoc.data()

  if (fvCreds.acceptedUsers.includes(userData.fvUserId)) {
    return {
      firstName: userData.firstName,
      lastName: userData.lastName,
      fvUserId: userData.fvUserId,
      ...fvCreds
    }
  } else {
    console.error("User Unauthorized in Tenant")
  }
}

export default getCredsFromFirestore
