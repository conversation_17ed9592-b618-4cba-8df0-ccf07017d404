import { getFirestore } from "firebase-admin/firestore"

/**
 * Creates a phase-order document in Firestore if it doesn't exist
 * @param {string} tenant - The tenant identifier
 * @param {string} projectTypeId - The project type ID
 * @param {Object} phaseListData - The phase list data from Filevine API
 * @returns {Object} The created phase order document data
 */

const createPhaseOrder = async (tenant, projectTypeId, phaseListData) => {
  const db = getFirestore()

  // Format the phase list data to match the expected structure
  const formattedPhaseList = {
    items: phaseListData.items.map((phase) => {
      return {
        phaseId: phase.phaseId,
        name: phase.name,
        isPermanent: phase.isPermanent,
        links: phase.links || {},
        draggableId: phase.phaseId.native.toString()
      }
    })
  }

  // Save the formatted data to Firestore
  const phaseOrderRef = db.collection(tenant).doc("kanban").collection(projectTypeId).doc("phase-order")
  await phaseOrderRef.set(formattedPhaseList)

  return formattedPhaseList
}

export default createPhaseOrder
