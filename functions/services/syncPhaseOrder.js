import { getFirestore } from "firebase-admin/firestore"

/**
 * Synchronizes the phase order in Firebase with the phase list from Filevine
 * - Adds new phases at the beginning of the list
 * - Updates renamed phases
 * - Removes phases that no longer exist in Filevine
 * - Preserves custom properties like usePhase, averageDays, etc.
 *
 * @param {string} tenant - The tenant identifier
 * @param {string} projectTypeId - The project type ID
 * @param {Object} filevinePhaseList - The phase list data from Filevine API
 * @param {Object|null} existingPhaseOrder - The existing phase order from Firebase (if any)
 * @returns {Object} The updated phase order document data
 */
const syncPhaseOrder = async (tenant, projectTypeId, filevinePhaseList, existingPhaseOrder = null) => {
  const db = getFirestore()

  // Create a map of Filevine phases by ID for easy lookup
  const filevinePhaseMap = new Map()
  filevinePhaseList.items.forEach((phase) => {
    filevinePhaseMap.set(phase.phaseId.native.toString(), phase)
  })

  let updatedPhaseItems = []

  if (existingPhaseOrder && existingPhaseOrder.items && existingPhaseOrder.items.length > 0) {
    // Filter out phases that no longer exist in Filevine and update renamed phases
    updatedPhaseItems = existingPhaseOrder.items
      .filter((phase) => filevinePhaseMap.has(phase.draggableId))
      .map((phase) => {
        const filevinePhase = filevinePhaseMap.get(phase.draggableId)
        // Update the phase name if it has changed
        if (filevinePhase.name !== phase.name) {
          return {
            ...phase, // This preserves all existing properties like usePhase, averageDays, etc.
            name: filevinePhase.name,
            isPermanent: filevinePhase.isPermanent
          }
        }
        return phase
      })

    // Find new phases that exist in Filevine but not in the existing phase order
    const existingPhaseIds = new Set(updatedPhaseItems.map((phase) => phase.draggableId))
    const newPhases = filevinePhaseList.items
      .filter((phase) => !existingPhaseIds.has(phase.phaseId.native.toString()))
      .map((phase) => {
        // Create the base phase object with required properties
        const phaseObj = {
          phaseId: phase.phaseId,
          name: phase.name,
          isPermanent: phase.isPermanent,
          links: phase.links || {},
          draggableId: phase.phaseId.native.toString()
        }

        // Only add optional properties if they are defined
        if (phase.goal !== undefined) phaseObj.goal = phase.goal
        if (phase.phaseDeadline !== undefined) phaseObj.phaseDeadline = phase.phaseDeadline
        if (phase.averageDays !== undefined) phaseObj.averageDays = phase.averageDays

        return phaseObj
      })

    // Add new phases at the beginning of the list
    updatedPhaseItems = [...newPhases, ...updatedPhaseItems]
  } else {
    // If there's no existing phase order, create a new one from the Filevine phase list
    updatedPhaseItems = filevinePhaseList.items.map((phase) => {
      // Create the base phase object with required properties
      const phaseObj = {
        phaseId: phase.phaseId,
        name: phase.name,
        isPermanent: phase.isPermanent,
        links: phase.links || {},
        draggableId: phase.phaseId.native.toString()
      }

      // Only add optional properties if they are defined
      if (phase.goal !== undefined) phaseObj.goal = phase.goal
      if (phase.phaseDeadline !== undefined) phaseObj.phaseDeadline = phase.phaseDeadline
      if (phase.averageDays !== undefined) phaseObj.averageDays = phase.averageDays

      return phaseObj
    })
  }

  // Clean the phase items to remove any undefined values
  const cleanedPhaseItems = updatedPhaseItems.map((phase) => {
    // Create a new object with only defined values
    const cleanPhase = {}
    Object.keys(phase).forEach((key) => {
      if (phase[key] !== undefined) {
        cleanPhase[key] = phase[key]
      }
    })
    return cleanPhase
  })

  // Create the updated phase order document
  const updatedPhaseOrder = {
    items: cleanedPhaseItems
  }

  // Save the updated phase order to Firestore
  const phaseOrderRef = db.collection(tenant).doc("kanban").collection(projectTypeId).doc("phase-order")
  await phaseOrderRef.set(updatedPhaseOrder)

  return updatedPhaseOrder
}

export default syncPhaseOrder
