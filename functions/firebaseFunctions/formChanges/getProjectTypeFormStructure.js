/**
 * Retrieves all sections for a project type along with their form data keys
 *
 * This function gets all sections for a project type and samples form data
 * to discover available fields for form change tracking configuration.
 */

import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectTypeSectionList from "../../services/filevine/getProjectTypeSectionList.js"
import getProjectList from "../../services/filevine/getProjectList.js"
import getForm from "../../services/filevine/getForm.js"

export const getProjectTypeFormStructure = v2.https.onCall(async (request) => {
  // Check authentication
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const { projectTypeId } = request.data || {}

  if (!projectTypeId) {
    throw new v2.https.HttpsError("invalid-argument", "projectTypeId is required")
  }

  // Get user credentials from Firestore
  let fvData = null
  try {
    fvData = await getCredsFromFirestore(userEmail)
  } catch (error) {
    console.error(error, "Error getting data from Firestore")
    throw new v2.https.HttpsError("internal", "Error retrieving user credentials")
  }

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // Create session with Filevine
  let accessToken, refreshToken
  try {
    if (fvData.pat) {
      const sessionData = await createSessionPAT(fvData)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(fvData)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }
  } catch (error) {
    console.error(error, "Error creating session")
    throw new v2.https.HttpsError("internal", "Error creating session with Filevine")
  }

  try {
    // Get all sections for the project type
    const sectionsData = await getProjectTypeSectionList(accessToken, refreshToken, fvData, projectTypeId)

    if (!sectionsData || !sectionsData.items) {
      throw new v2.https.HttpsError("not-found", "No sections found for this project type")
    }

    // Filter to only form sections (not collections)
    const formSections = sectionsData.items.filter((section) => !section.isCollection)

    if (formSections.length === 0) {
      return {
        success: true,
        projectTypeId: projectTypeId,
        sections: [],
        message: "No form sections found for this project type"
      }
    }

    // Optimized sample project discovery - fetch in batches until we find our target project type
    let sampleProject = null
    let hasMore = true
    let offset = 0
    const batchSize = 1000
    let totalProjectsScanned = 0

    while (!sampleProject && hasMore) {
      const projectsData = await getProjectList(accessToken, refreshToken, fvData, batchSize, offset)
      totalProjectsScanned += projectsData.items.length

      // Deduplicate projects by projectTypeId within this batch
      const uniqueProjectsInBatch = new Map()

      for (const project of projectsData.items) {
        if (project.projectTypeId && project.projectTypeId.native) {
          const typeId = project.projectTypeId.native.toString()
          if (!uniqueProjectsInBatch.has(typeId)) {
            uniqueProjectsInBatch.set(typeId, project)
          }
        }
      }

      // Look for our target project type in this batch
      sampleProject = uniqueProjectsInBatch.get(projectTypeId.toString())

      if (sampleProject) {
        break
      }

      // Continue to next batch if we haven't found our target and there are more projects
      if (projectsData.hasMore) {
        offset += batchSize
        hasMore = true
      } else {
        hasMore = false
      }
    }

    if (!sampleProject) {
      return {
        success: true,
        projectTypeId: projectTypeId,
        sections: formSections.map((section) => ({
          sectionSelector: section.sectionSelector,
          name: section.name,
          formFields: [],
          error: "No sample project found to discover form fields"
        }))
      }
    }

    // Get form data for each section to discover available fields
    const sectionsWithFields = []

    for (const section of formSections) {
      try {
        const formData = await getForm(
          accessToken,
          refreshToken,
          fvData,
          sampleProject.projectId.native,
          section.sectionSelector
        )

        // Extract field information
        const formFields = []
        if (formData) {
          // Get all keys except metadata fields
          const fieldKeys = Object.keys(formData).filter(
            (key) => key !== "name" && key !== "sectionSelector" && typeof formData[key] !== "function"
          )

          for (const fieldKey of fieldKeys) {
            const fieldValue = formData[fieldKey]
            let fieldType = "unknown"

            // Determine field type based on value
            if (fieldValue === null || fieldValue === undefined) {
              fieldType = "nullable"
            } else if (typeof fieldValue === "string") {
              // Check if it's a date string
              if (fieldValue.includes("T") && fieldValue.includes("Z")) {
                fieldType = "date"
              } else {
                fieldType = "text"
              }
            } else if (typeof fieldValue === "number") {
              fieldType = "number"
            } else if (typeof fieldValue === "boolean") {
              fieldType = "boolean"
            } else if (typeof fieldValue === "object") {
              fieldType = "object"
            }

            formFields.push({
              fieldKey: fieldKey,
              fieldType: fieldType,
              sampleValue: fieldValue,
              hasValue: fieldValue !== null && fieldValue !== undefined && fieldValue !== ""
            })
          }
        }

        sectionsWithFields.push({
          sectionSelector: section.sectionSelector,
          name: section.name,
          formFields: formFields,
          totalFields: formFields.length,
          fieldsWithValues: formFields.filter((f) => f.hasValue).length
        })
      } catch (error) {
        console.error(`Error getting form data for section ${section.sectionSelector}:`, error)
        sectionsWithFields.push({
          sectionSelector: section.sectionSelector,
          name: section.name,
          formFields: [],
          error: `Failed to retrieve form data: ${error.message}`
        })
      }
    }

    return {
      success: true,
      projectTypeId: projectTypeId,
      sampleProjectId: sampleProject.projectId.native,
      sampleProjectName: sampleProject.clientName || sampleProject.projectName || "Unknown",
      sections: sectionsWithFields,
      totalSections: sectionsWithFields.length,
      totalFields: sectionsWithFields.reduce((sum, section) => sum + section.formFields.length, 0),
      totalProjectsScanned: totalProjectsScanned
    }
  } catch (error) {
    console.error("Error getting project type form structure:", error)
    throw new v2.https.HttpsError("internal", `Error retrieving form structure: ${error.message}`)
  }
})
