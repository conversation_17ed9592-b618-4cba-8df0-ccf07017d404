/**
 * Handles form change events from Filevine webhooks
 *
 * This function receives webhook events for form changes,
 * processes the data, and logs it for now (no Firestore until connection is confirmed).
 */

import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getForm from "../../services/filevine/getForm.js"
import getUser from "../../services/filevine/getUser.js"

/**
 * Cloud Function to handle form change webhook events
 */
export const formChangeHandler = v2.https.onRequest(async (req, res) => {
  const db = getFirestore()

  // Log the incoming webhook event for debugging
  console.log(`=== WEBHOOK EVENT RECEIVED ===`)
  console.log(`Object Type: "${req.body.Object}"`)
  console.log(`Event Type: "${req.body.Event}"`)
  console.log(`Tenant: "${req.params[0]}"`)
  console.log("Full event body:", JSON.stringify(req.body, null, 2))
  console.log(`=== END WEBHOOK EVENT ===`)

  // Extract tenant from the URL path
  const tenant = req.params[0]
  const eventBody = req.body

  try {
    // Get tenant credentials
    const credsRef = db.collection(tenant).doc("fv-creds")
    const credsDoc = await credsRef.get()

    if (!credsDoc.exists) {
      console.error(`No credentials found for tenant: ${tenant}`)
      res.status(500).send("Tenant credentials not found")
      return
    }

    const creds = credsDoc.data()

    // Create session with Filevine
    let accessToken, refreshToken
    if (creds.pat) {
      const sessionData = await createSessionPAT(creds)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(creds)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }

    // Get user information
    const userData = await getUser(accessToken, refreshToken, creds, eventBody.UserId)
    const userFullName = `${userData.user.firstName} ${userData.user.lastName ?? ""}`

    // Get form data to see what changed
    const formData = await getForm(
      accessToken,
      refreshToken,
      creds,
      eventBody.ProjectId,
      eventBody.ObjectId.SectionSelector
    )

    // Log the form change data to verify the connection works
    console.log("=== FORM CHANGE DETECTED ===")
    console.log("Tenant:", tenant)
    console.log("Project ID:", eventBody.ProjectId)
    console.log("Section Selector:", eventBody.ObjectId.SectionSelector)
    console.log("User ID:", eventBody.UserId)
    console.log("User Name:", userFullName)
    console.log("Timestamp:", eventBody.Timestamp)
    console.log("Form Data Keys:", Object.keys(formData))
    console.log("Changed Fields:", eventBody.Other?.ChangedFields || "Not provided")
    console.log("Full Form Change Data:", JSON.stringify(formData, null, 2))
    console.log("Event Body:", JSON.stringify(eventBody, null, 2))
    console.log("=== END FORM CHANGE DATA ===")

    res.status(200).send("Form change processed successfully")
  } catch (error) {
    console.error("Error processing form change:", error)
    res.status(500).send("Error processing form change")
  }
})
