import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import getTimeCodeMap from "../../services/getTimeCodeMap.js"

export const gettimecodemap = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let timeCodeMap

  await getTimeCodeMap(fvData.tenant)
    .then((data) => {
      if (data) {
        timeCodeMap = data
      }
    })
    .catch((err) => console.error(err, "error getting project type data from firestore"))

  return {
    timeCodeMap: timeCodeMap
  }
})
