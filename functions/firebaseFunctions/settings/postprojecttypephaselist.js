import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const postprojecttypephaselist = v2.https.onCall(async (request) => {
  const db = getFirestore()
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const projectTypeId = request.data.projectTypeId.toString()
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  await db
    .collection(fvData.tenant)
    .doc("kanban")
    .collection(projectTypeId)
    .doc("phase-order")
    .set({
      items: request.data.newPhaseOrder
    })
    .catch((error) => console.error(error, "error updating phase order"))
})
