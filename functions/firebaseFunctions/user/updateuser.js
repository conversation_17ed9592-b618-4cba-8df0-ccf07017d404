import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"

export const updateuser = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const updateObj = {}

  if (request.data.tenant) {
    updateObj.tenant = request.data.tenant
  }

  if (request.data.defaultProjectType) {
    Object.keys(request.data.defaultProjectType).forEach((tenant) => {
      updateObj[`defaultProjectType.${tenant}`] = request.data.defaultProjectType[tenant]
    })
  }
  const db = getFirestore()

  await db.collection("users").doc(userEmail).update(updateObj)

  return
})
