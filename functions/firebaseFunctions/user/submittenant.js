import * as v2 from "firebase-functions/v2"
import { getFirestore, FieldValue } from "firebase-admin/firestore"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getUserOrgId from "../../services/filevine/getUserOrgId.js"

export const submittenant = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let userCreds

  const db = getFirestore()

  await db
    .collection("users")
    .doc(userEmail)
    .get()
    .then((data) => {
      userCreds = data.data()
    })
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!userCreds.ropsAdmin) {
    throw new v2.https.HttpsError("failed-precondition", "Not Authorized")
  }

  let pat = request.data.pat
  let tenant = request.data.tenant
  let managedServiceProjectId = request.data.managedServiceProjectId

  let accessToken = null

  await createSessionPAT(request.data)
    .then((data) => {
      accessToken = data.access_token
    })
    .catch((err) => console.error(err, "Invalid PAT"))

  let userOrgId

  await getUserOrgId(accessToken)
    .then((data) => {
      userOrgId = data
    })
    .catch((err) => console.error(err, "error getting user org id"))

  await db
    .collection(tenant)
    .doc("fv-creds")
    .set({
      orgId: userOrgId.orgs[0].orgId,
      pat: pat,
      serviceAcctUserId: userOrgId.user.userId.native,
      tenant: tenant,
      acceptedUsers: [userCreds.fvUserId],
      managedServiceProjectId: managedServiceProjectId
    })
    .catch((err) => console.error(err))

  await db
    .collection("users")
    .doc(userEmail)
    .set(
      {
        tenantAccessList: FieldValue.arrayUnion(tenant)
      },
      { merge: true }
    )
  return
})
