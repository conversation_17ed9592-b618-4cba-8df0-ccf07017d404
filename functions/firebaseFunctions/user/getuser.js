import * as v2 from "firebase-functions/v2"
import { getFirestore } from "firebase-admin/firestore"

export const getuser = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let userData

  const db = getFirestore()
  await db
    .collection("users")
    .doc(userEmail)
    .get()
    .then((docs) => {
      const data = docs.data()
      userData = {
        ropsAdmin: data.ropsAdmin,
        tenantAccessList: data?.tenantAccessList ? data.tenantAccessList : null,
        tenant: data.tenant,
        defaultProjectType: data.defaultProjectType
      }
    })

  return {
    userData: userData
  }
})
