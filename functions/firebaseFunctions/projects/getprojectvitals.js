import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectVitals from "../../services/filevine/getProjectVitals.js"
import getProjectNoteList from "../../services/filevine/getProjectNoteList.js"
import getProjectTeam from "../../services/filevine/getProjectTeam.js"
import { getFirestore } from "firebase-admin/firestore"

export const getprojectvitals = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }
  const db = getFirestore()

  const userEmail = request.auth.token.email
  const projectId = request.data.projectId

  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  let vitals
  let projectTeam
  let projectTeamObject = { 1: "Filevine System" }

  await getProjectVitals(accessToken, refreshToken, fvData, projectId)
    .then((data) => (vitals = data))
    .catch((err) => console.error(err, "error getting vitals"))

  await getProjectTeam(accessToken, refreshToken, fvData, projectId)
    .then((data) => {
      projectTeam = data.items
      for (let i = 0; i < data.items.length; i++) {
        projectTeamObject[data.items[i].userId.native] = data.items[i].fullname
      }
    })
    .catch((err) => console.error(err, "error getting project team"))

  let projectNoteList = []

  await getProjectNoteList(accessToken, refreshToken, fvData, projectId)
    .then((data) => {
      for (let i = 0; i < data.items.length; i++) {
        const note = data.items[i]
        const createdUtcDateTime = new Date(note.createdAt)
        const noteObj = {
          author: projectTeamObject[note.authorId.native],
          body: note.body,
          createdDate: `Posted: ${createdUtcDateTime.toString().slice(0, 15)}`
        }
        if (note.assigneeId) {
          noteObj.assignee = projectTeamObject[note.assigneeId.native]

          if (note.isCompleted) {
            const completedUtcDateTime = new Date(note.lastActivity)
            noteObj.completedOrTarget = `Completed: ${completedUtcDateTime.toString().slice(0, 15)}`
          } else {
            const targetUtcDateTime = new Date(note.targetDate)
            noteObj.completedOrTarget = `Set for: ${targetUtcDateTime.toString().slice(0, 15)}`
          }
        }

        // console.log(noteObj, "note obj")
        projectNoteList.push(noteObj)
      }
    })
    .catch((err) => console.error(err, "error getting vitals"))

  let phaseHistory = []

  await db
    .collection(fvData.tenant)
    .doc("project-data")
    .collection(`${projectId}`)
    .doc("phase-data")
    .get()
    .then((data) => {
      phaseHistory = [...data.data().allPhaseChanges]
    })
    .catch(() => console.error("no phases data"))

  const millisecondsPerDay = 24 * 60 * 60 * 1000

  for (let i = 0; i < phaseHistory.length; i++) {
    let change = phaseHistory[i]
    let timeSpent = change.toTimestamp - change.fromTimestamp
    timeSpent = (timeSpent / millisecondsPerDay) * 10

    let utcDateTime = new Date(change.toTimestamp - 25200000)

    phaseHistory[i].totalDaysSpent = Math.ceil(timeSpent) / 10
    phaseHistory[i].dateChanged = utcDateTime.toString().slice(0, 15)

    if (phaseHistory[i].userId) {
      phaseHistory[i].userFullname = projectTeamObject[phaseHistory[i].userId]
    }
  }

  return {
    vitals: vitals,
    projectTeam: projectTeam,
    projectNoteList: projectNoteList,
    phaseHistory: phaseHistory
  }
})
