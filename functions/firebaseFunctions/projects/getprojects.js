import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectList from "../../services/filevine/getProjectList.js"

export const getprojects = v2.https.onCall(async (request) => {
  const db = getFirestore()
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => {
      throw new v2.https.HttpsError("failed", "Error getting credentials from Firestore", error)
    })

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let firestoreProjectPhaseData

  await db
    .collection(fvData.tenant)
    .doc("project-data")
    .get()
    .then((data) => (firestoreProjectPhaseData = data.data()))

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => {
        throw new Error("failed", "Error creating session", err)
      })
  }

  let projectData = []
  let hasMore = true
  let offset = 0

  while (hasMore) {
    await getProjectList(accessToken, refreshToken, fvData, 1000, offset)
      .then((data) => {
        projectData.push(...data.items)
        hasMore = data.hasMore
        offset = offset + 1000
      })
      .catch((err) => {
        hasMore = false
        throw new Error("failed", "Error getting project list", err)
      })
  }

  const millisecondsPerDay = 24 * 60 * 60 * 1000
  let epochNow = new Date().getTime()

  for (let i = 0; i < projectData.length; i++) {
    if (firestoreProjectPhaseData && firestoreProjectPhaseData[projectData[i].projectId.native]) {
      projectData[i].lastPhaseChange = firestoreProjectPhaseData[projectData[i].projectId.native]

      let milliSecondDiff =
        epochNow - firestoreProjectPhaseData[projectData[i].projectId.native].lastPhaseChangeTimestamp
      let daySpent = (milliSecondDiff / millisecondsPerDay) * 10
      projectData[i].daysSpentInPhase = Math.ceil(daySpent) / 10
    }

    let createdDate = new Date(projectData[i].createdDate)
    let day = createdDate.getDate()
    let month = createdDate.getMonth() + 1
    let year = createdDate.getFullYear()
    let createdEpoch = createdDate.getTime()

    let timeActive = (epochNow - createdEpoch) / millisecondsPerDay

    projectData[i].formattedDate = `${month}/${day}/${year} (${Math.round(timeActive)} days)`
  }

  return {
    projectData: projectData
  }
})
