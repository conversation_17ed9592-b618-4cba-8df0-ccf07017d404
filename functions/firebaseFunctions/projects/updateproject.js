import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import updateProject from "../../services/filevine/updateProject.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import createNote from "../../services/filevine/createNote.js"

export const updateproject = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const projectId = request.data.projectId
  const newPhase = request.data.newPhase
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  let updatedProject

  await updateProject(accessToken, refreshToken, fvData, projectId, newPhase)
    .then((data) => (updatedProject = data))
    .catch((err) => console.error(err, "error"))

  const user = fvData.firstName ? `${fvData.firstName} ${fvData.lastName}` : userEmail

  const noteBody = {
    projectId: {
      native: projectId
    },
    body: `___${user}___ has changed the project phase to ___${updatedProject.phaseName}___ using Legalware.ai`
  }

  await createNote(accessToken, refreshToken, fvData, noteBody).catch((err) =>
    console.error(err, "error posting note")
  )

  return updatedProject
})
