import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectTypeList from "../../services/filevine/getProjectTypeList.js"

export const getprojecttypelist = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => {
      throw new Error("Error getting credentials from Firestore", error)
    })

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => {
        throw new Error("Error creating session", err)
      })
  }

  let projectTypeData

  await getProjectTypeList(accessToken, refreshToken, fvData)
    .then((data) => {
      projectTypeData = data
    })
    .catch((err) => {
      throw new Error("Error getting project type list", err)
    })

  return projectTypeData
})
