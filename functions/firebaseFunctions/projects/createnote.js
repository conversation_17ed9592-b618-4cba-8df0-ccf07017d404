import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import createNote from "../../services/filevine/createNote.js"
import getUser from "../../services/filevine/getUser.js"

export const createnote = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const noteBody = request.data

  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  const noteObj = noteBody

  if (fvData.fvUserId) {
    noteObj.authorId = { native: fvData.fvUserId }
  }

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  let successNote = {
    author: `${fvData.firstName} ${fvData.lastName}`
  }

  await createNote(accessToken, refreshToken, fvData, noteObj)
    .then((data) => {
      // console.log(data, "new task data")
      const formattedCreatedDate = new Date(data.createdAt)
      successNote.body = data.body
      successNote.createdDate = `Posted: ${formattedCreatedDate.toString().slice(0, 15)}`
      if (data.assigneeId) {
        const formattedTargetDate = new Date(data.targetDate)
        successNote.completedOrTarget = `Set for: ${formattedTargetDate.toString().slice(0, 15)}`
        successNote.assigneeId = data.assigneeId.native
      }
    })
    .catch((err) => console.error(err, "error"))

  if (successNote.assigneeId) {
    await getUser(accessToken, refreshToken, fvData, successNote.assigneeId)
      .then((data) => {
        successNote.assignee = `${data.user.firstName} ${data.user.lastName}`
      })
      .catch((err) => console.error(err, "error"))
  }

  return successNote
})
