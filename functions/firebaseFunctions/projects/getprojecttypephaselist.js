import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectTypePhaseList from "../../services/filevine/getProjectTypePhaseList.js"
import getPhaseOrder from "../../services/getPhaseOrder.js"
import createPhaseOrder from "../../services/createPhaseOrder.js"
import { getFirestore } from "firebase-admin/firestore"

export const getprojecttypephaselist = v2.https.onCall(async (request) => {
  const db = getFirestore()
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }
  const userEmail = request.auth.token.email
  const projectTypeId = request.data.projectTypeId.toString()
  let fvData = null
  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))
  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }
  let projectTypePhaseData
  try {
    const data = await getPhaseOrder(fvData.tenant, projectTypeId)
    projectTypePhaseData = {
      count: data.items.length,
      items: data.items
    }
  } catch (err) {
    console.error("Phase order not found, fetching from Filevine and creating it", err)

    // Get phase list from Filevine
    let accessToken
    let refreshToken
    if (fvData.pat) {
      const sessionData = await createSessionPAT(fvData)
      accessToken = sessionData.access_token
    } else {
      const sessionData = await createSession(fvData)
      accessToken = sessionData.accessToken
      refreshToken = sessionData.refreshToken
    }

    const filevinePhaseList = await getProjectTypePhaseList(accessToken, refreshToken, fvData, projectTypeId)

    if (filevinePhaseList) {
      // Create the phase order document
      const createdPhaseOrder = await createPhaseOrder(fvData.tenant, projectTypeId, filevinePhaseList)

      projectTypePhaseData = {
        count: createdPhaseOrder.items.length,
        items: createdPhaseOrder.items
      }
    } else {
      console.error("Failed to fetch phase list from Filevine")
    }
  }
  let phaseAverages
  await db
    .collection(fvData.tenant)
    .doc("kanban")
    .collection(projectTypeId)
    .doc("phase-averages")
    .get()
    .then((data) => (phaseAverages = data.data()))
    .catch(() => console.error("no phase average data"))
  // Add phase averages to the phase list if available
  if (projectTypePhaseData && phaseAverages) {
    for (let i = 0; i < projectTypePhaseData.items.length; i++) {
      if (phaseAverages[projectTypePhaseData.items[i].phaseId.native]) {
        let sum = 0
        for (let value of phaseAverages[projectTypePhaseData.items[i].phaseId.native]) {
          sum += value
        }
        projectTypePhaseData.items[i].averageDays = Math.round(
          sum / phaseAverages[projectTypePhaseData.items[i].phaseId.native].length
        )
      }
    }
  }

  return projectTypePhaseData
})
