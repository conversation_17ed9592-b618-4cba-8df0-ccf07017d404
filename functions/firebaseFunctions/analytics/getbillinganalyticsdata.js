import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getOrgBillingItems from "../../services/filevine/getOrgBillingItems.js"
import getUserList from "../../services/filevine/getUserList.js"
import getProject from "../../services/filevine/getProject.js"
import getTimeCodeMap from "../../services/getTimeCodeMap.js"

export const getbillinganalyticsdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const palette = request.data.palette
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  // =========================================
  // BUILD PROJECT LIST
  // =========================================

  let epochToday = Date.now()

  const oneDayMilliseconds = 86400000
  const oneWeekMilliseconds = 604800000

  const msSinceSunday = oneDayMilliseconds * new Date().getDay()

  const epochLastSunday = epochToday - msSinceSunday

  const msSincePrevSunday = msSinceSunday + oneWeekMilliseconds

  let epochOneWeek = new Date(epochToday - msSincePrevSunday)
  epochOneWeek = `${epochOneWeek.getFullYear()}-${epochOneWeek.getMonth() + 1}-${epochOneWeek.getDate()}`

  // =========================================
  // BUILD PROJECT LIST
  // =========================================

  let billingItems = []
  let offset = 0
  let hasMore = true

  while (hasMore) {
    await getOrgBillingItems(accessToken, refreshToken, fvData, 1000, offset, epochOneWeek)
      .then((data) => {
        billingItems.push(...data.items)
        hasMore = data.hasMore
        offset = offset + 1000
      })
      .catch((err) => {
        console.error(err, "error")
        hasMore = false
      })
  }

  let timeCodeMap

  await getTimeCodeMap(fvData.tenant)
    .then((data) => {
      if (data) {
        timeCodeMap = data
      }
    })
    .catch((err) => console.error(err, "error getting project type data from firestore"))

  let totalHoursThisWeek = 0
  const topProjects = {}
  const billingTotalsByUser = {}
  let timeCodeIndex = 1
  let paletteIndex = 0
  let timeCodeStackUser = [
    {
      data: [],
      stack: "A",
      label: "No Time Code",
      color: palette[paletteIndex]
    }
  ]
  let timeCodeStackWeekday = [
    {
      data: [],
      stack: "A",
      label: "No Time Code",
      color: palette[paletteIndex]
    }
  ]
  const timeCodeIndexObj = {
    noTimeCode: 0
  }
  const billingTotalsByWeekday = {
    thisWeek: {
      0: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      1: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      2: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      3: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      4: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      5: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      },
      6: {
        quantity: 0,
        timeCodeData: {
          noTimeCode: {
            quantity: 0
          }
        }
      }
    },
    lastWeek: {
      0: {
        quantity: 0
      },
      1: {
        quantity: 0
      },
      2: {
        quantity: 0
      },
      3: {
        quantity: 0
      },
      4: {
        quantity: 0
      },
      5: {
        quantity: 0
      },
      6: {
        quantity: 0
      }
    }
  }

  for (let i = 0; i < billingItems.length; i++) {
    let fieldValues = billingItems[i].fieldValues

    if (billingItems[i].billingType === "Time") {
      let billingEntryObj = {}

      for (let j = 0; j < fieldValues.length; j++) {
        if (fieldValues[j].fieldSelector === "BillingItemUser") {
          billingEntryObj.user = fieldValues[j].value
        }
        if (fieldValues[j].fieldSelector === "BillingItemQuantity") {
          billingEntryObj.quantity = fieldValues[j].value
        }
        if (fieldValues[j].fieldSelector === "TimeCode") {
          billingEntryObj.timeCode = fieldValues[j].value
        }
        if (fieldValues[j].fieldSelector === "BillingItemDate") {
          billingEntryObj.date = new Date(fieldValues[j].value)
          billingEntryObj.epochDate = new Date(fieldValues[j].value).valueOf()
        }
      }

      if (billingEntryObj.user) {
        if (!billingTotalsByUser[billingEntryObj.user]) {
          billingTotalsByUser[billingEntryObj.user] = {
            thisWeek: {
              quantity: 0,
              timeCodeData: {
                noTimeCode: {
                  quantity: 0
                }
              }
            },
            lastWeek: {
              quantity: 0
            }
          }
        }

        if (billingEntryObj.epochDate > epochLastSunday) {
          billingTotalsByUser[billingEntryObj.user].thisWeek.quantity += billingEntryObj.quantity
          billingTotalsByWeekday.thisWeek[billingEntryObj.date.getDay()].quantity += billingEntryObj.quantity

          if (!topProjects[billingItems[i].projectId]) {
            topProjects[billingItems[i].projectId] = billingEntryObj.quantity
          } else {
            topProjects[billingItems[i].projectId] += billingEntryObj.quantity
          }

          if (billingEntryObj.timeCode) {
            if (
              billingTotalsByWeekday.thisWeek[billingEntryObj.date.getDay()].timeCodeData[
                billingEntryObj.timeCode
              ]
            ) {
              billingTotalsByWeekday.thisWeek[billingEntryObj.date.getDay()].timeCodeData[
                billingEntryObj.timeCode
              ].quantity += billingEntryObj.quantity
            } else {
              billingTotalsByWeekday.thisWeek[billingEntryObj.date.getDay()].timeCodeData[
                billingEntryObj.timeCode
              ] = {
                quantity: billingEntryObj.quantity
              }
            }
            if (billingTotalsByUser[billingEntryObj.user].thisWeek.timeCodeData[billingEntryObj.timeCode]) {
              billingTotalsByUser[billingEntryObj.user].thisWeek.timeCodeData[
                billingEntryObj.timeCode
              ].quantity += billingEntryObj.quantity
            } else {
              billingTotalsByUser[billingEntryObj.user].thisWeek.timeCodeData[billingEntryObj.timeCode] = {
                quantity: billingEntryObj.quantity
              }
            }
            if (timeCodeMap && !timeCodeIndexObj[billingEntryObj.timeCode]) {
              timeCodeIndexObj[billingEntryObj.timeCode] = timeCodeIndex
              timeCodeIndex++
              if (paletteIndex >= palette.length) {
                paletteIndex = 0
              } else {
                paletteIndex++
              }
              timeCodeStackUser.push({
                data: [],
                stack: "A",
                label: timeCodeMap[billingEntryObj.timeCode]
                  ? `${billingEntryObj.timeCode} | ${timeCodeMap[billingEntryObj.timeCode]}`
                  : billingEntryObj.timeCode,
                color: palette[paletteIndex]
              })
              timeCodeStackWeekday.push({
                data: [],
                stack: "A",
                label: timeCodeMap[billingEntryObj.timeCode]
                  ? `${billingEntryObj.timeCode} | ${timeCodeMap[billingEntryObj.timeCode]}`
                  : billingEntryObj.timeCode,
                color: palette[paletteIndex]
              })
            }
          } else {
            billingTotalsByUser[billingEntryObj.user].thisWeek.timeCodeData.noTimeCode.quantity +=
              billingEntryObj.quantity
            billingTotalsByWeekday.thisWeek[billingEntryObj.date.getDay()].timeCodeData.noTimeCode.quantity +=
              billingEntryObj.quantity
          }

          totalHoursThisWeek += billingEntryObj.quantity
        }

        if (billingEntryObj.epochDate < epochLastSunday) {
          billingTotalsByUser[billingEntryObj.user].lastWeek.quantity += billingEntryObj.quantity
          billingTotalsByWeekday.lastWeek[billingEntryObj.date.getDay()].quantity += billingEntryObj.quantity
        }
      }
    }
  }

  let userIdObj = {}

  await getUserList(accessToken, refreshToken, fvData)
    .then((data) => {
      for (let i = 0; i < data.items.length; i++) {
        let user = data.items[i].user
        userIdObj[user.userId.native] = {
          username: user?.username,
          firstName: user?.firstName,
          lastName: user?.lastName ? user.lastName : ""
        }
      }
    })
    .catch((err) => console.error(err, "error"))

  let billingUserArray = []
  let billingUserEntryQuantityThisWeek = []
  let billingUserEntryQuantityLastWeek = []

  for (let uid in billingTotalsByUser) {
    if (userIdObj[uid]) {
      billingUserArray.push(userIdObj[uid].username)
      billingUserEntryQuantityThisWeek.push(billingTotalsByUser[uid].thisWeek.quantity)
      billingUserEntryQuantityLastWeek.push(billingTotalsByUser[uid].lastWeek.quantity)

      let timeCodeObj = billingTotalsByUser[uid].thisWeek.timeCodeData

      for (let timeCode in timeCodeIndexObj) {
        let timeCodeIndex = timeCodeIndexObj[timeCode]

        if (timeCodeObj[timeCode]) {
          timeCodeStackUser[timeCodeIndex].data.push(timeCodeObj[timeCode].quantity)
          // console.log(timeCodeStackUser[timeCodeIndex], "time code found for user")
        } else {
          // console.log(timeCodeStackUser[timeCodeIndex], "time code not found for user")
          timeCodeStackUser[timeCodeIndex].data.push(0)
        }
      }
    }
  }

  let billingWeekdayQuantityThisWeek = []

  for (let day in billingTotalsByWeekday.thisWeek) {
    billingWeekdayQuantityThisWeek.push(billingTotalsByWeekday.thisWeek[day].quantity)

    let timeCodeObj = billingTotalsByWeekday.thisWeek[day].timeCodeData

    for (let timeCode in timeCodeIndexObj) {
      let timeCodeIndex = timeCodeIndexObj[timeCode]

      if (timeCodeObj[timeCode]) {
        timeCodeStackWeekday[timeCodeIndex].data.push(timeCodeObj[timeCode].quantity)
        // console.log(timeCodeStackWeekday[timeCodeIndex], "time code found for user")
      } else {
        // console.log(timeCodeStackWeekday[timeCodeIndex], "time code not found for user")
        timeCodeStackWeekday[timeCodeIndex].data.push(0)
      }
    }
  }

  let billingWeekdayQuantityLastWeek = []

  for (let day in billingTotalsByWeekday.lastWeek) {
    billingWeekdayQuantityLastWeek.push(billingTotalsByWeekday.lastWeek[day].quantity)
  }

  let topProjectArray = []

  for (let projectId in topProjects) {
    const projObj = {
      value: topProjects[projectId],
      id: projectId
    }

    if (topProjectArray.length < 5) {
      topProjectArray.push(projObj)
    } else {
      for (let i = 0; i < topProjectArray.length; i++) {
        if (topProjectArray[i].value < projObj.value) {
          topProjectArray[i] = projObj
          break
        }
      }
    }
  }

  let hourRunningCount = 0

  for (let i = 0; i < topProjectArray.length; i++) {
    await getProject(accessToken, refreshToken, fvData, topProjectArray[i].id).then(
      (data) => (topProjectArray[i].label = data.projectName)
    )

    hourRunningCount += topProjectArray[i].value
  }

  if (totalHoursThisWeek - hourRunningCount) {
    topProjectArray.push({
      value: totalHoursThisWeek - hourRunningCount,
      id: "0",
      label: "Other"
    })
  }

  return {
    billingUserArray: billingUserArray,
    billingUserEntryQuantityThisWeek: billingUserEntryQuantityThisWeek,
    billingUserEntryQuantityLastWeek: billingUserEntryQuantityLastWeek,
    billingWeekdayQuantityThisWeek: billingWeekdayQuantityThisWeek,
    billingWeekdayQuantityLastWeek: billingWeekdayQuantityLastWeek,
    topProjectArray: topProjectArray,
    totalHoursThisWeek: totalHoursThisWeek,
    timeCodeStackUser: timeCodeStackUser,
    timeCodeStackWeekday: timeCodeStackWeekday
  }
})
