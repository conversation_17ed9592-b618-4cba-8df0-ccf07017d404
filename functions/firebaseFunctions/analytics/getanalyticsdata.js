import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectList from "../../services/filevine/getProjectList.js"
import getProjectTypePhaseList from "../../services/filevine/getProjectTypePhaseList.js"
import getPhaseOrder from "../../services/getPhaseOrder.js"

export const getanalyticsdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const projectTypeId = request.data.projectTypeId.toString()
  const palette = request.data.palette
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let projectTypePhaseData

  await getPhaseOrder(fvData.tenant, projectTypeId)
    .then((data) => {
      if (data) {
        projectTypePhaseData = {
          count: data.items.length,
          items: data.items
        }
      }
    })
    .catch((err) => console.error(err, "error getting project type data from firestore"))

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  // IF PHASE ORDER HAS NOT BEEN EDITED AND SAVED IN FIRESTORE
  if (!projectTypePhaseData) {
    await getProjectTypePhaseList(accessToken, refreshToken, fvData, projectTypeId)
      .then((data) => (projectTypePhaseData = data))
      .catch((err) => console.error(err, ""))
  }

  // =========================================
  // PHASE ORDER LOGIC
  // =========================================

  // array of 0's used to count each project a primary appears in according to phase
  let phaseOrderObj = {}
  // array of phases by name, used in bar chart
  let phaseArrayYAxis = []
  let phaseIndex = 0

  for (let i = 0; i < projectTypePhaseData.items.length; i++) {
    let phase = projectTypePhaseData.items[i]
    if (!("usePhase" in phase) || phase.usePhase) {
      phaseOrderObj[phase.phaseId.native] = phaseIndex
      phaseArrayYAxis.push(phase.name)
      phaseIndex++
    }
  }

  // ===========================================
  // BUILD PROJECT LIST
  // =========================================

  let projectList = []
  let offset = 0
  let hasMore = true

  while (hasMore) {
    await getProjectList(accessToken, refreshToken, fvData, 1000, offset)
      .then((data) => {
        projectList.push(...data.items)
        hasMore = data.hasMore
        offset = offset + 1000
      })
      .catch((err) => {
        console.error(err, "error")
        hasMore = false
      })
  }

  // =========================================
  // GET PAST 12 MONTH ARRAY
  // =========================================

  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth()

  const oneYearAgo = currentYear - 1
  const monthAbbreviations = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ]

  let createdDateObj = {}

  for (let year = oneYearAgo; year <= currentYear; year++) {
    // Determine the range of months to iterate through based on the current year
    const startMonth = year === oneYearAgo ? currentMonth + 1 : 0
    const endMonth = year === currentYear ? currentMonth : 11

    // Loop through each month and compile the data
    for (let month = startMonth; month <= endMonth; month++) {
      const currentMonth = monthAbbreviations[month]
      const yearMonth = `${year}-${(month + 1).toString().padStart(2, "0")}` // Format as "YYYY-MM"

      // Generate some example data (can be replaced with actual data retrieval)
      const monthData = {
        month: currentMonth,
        year: year,
        projectCount: 0
      }

      // Assign the data for the current month and year to the object
      createdDateObj[yearMonth] = monthData
    }
  }

  // =========================================
  // SORT PROJECT LIST
  // =========================================

  // used to track primary data displayed in both pie and bar charts
  let primaryObj = {}
  let hashtagObj = {}

  for (let i = 0; i < projectList.length; i++) {
    let createdMonth = projectList[i].createdDate.slice(0, 7)
    let primaryUsername = projectList[i].firstPrimaryUsername
    let phaseId = projectList[i].phaseId.native
    let projectType = projectList[i].projectTypeId.native.toString()
    let hashtagArray = projectList[i].hashtags

    if (projectType === projectTypeId && phaseId in phaseOrderObj) {
      if (createdDateObj[createdMonth]) {
        createdDateObj[createdMonth].projectCount++
      }

      let phaseIndex = phaseOrderObj[phaseId]

      if (primaryObj[primaryUsername]) {
        primaryObj[primaryUsername].projectCount++
        primaryObj[primaryUsername].projectCountByPhase[phaseIndex]++
      } else {
        primaryObj[primaryUsername] = {
          primaryName: projectList[i].firstPrimaryName,
          projectCount: 1,
          projectCountByPhase: new Array(phaseArrayYAxis.length).fill(0)
        }
        primaryObj[primaryUsername].projectCountByPhase[phaseIndex] = 1
      }

      if (hashtagArray.length) {
        for (let j = 0; j < hashtagArray.length; j++) {
          if (hashtagObj[hashtagArray[j]]) {
            hashtagObj[hashtagArray[j]]++
          } else {
            hashtagObj[hashtagArray[j]] = 1
          }
        }
      }
    }
  }

  // =========================================
  // STRUCTURE DATA
  // =========================================

  let primaryPhaseData = []
  let primaryProjectData = []
  let index = 0

  for (let username in primaryObj) {
    if (index >= palette.length) {
      index = 0
    } else {
      index++
    }

    let primaryPhaseObj = {
      data: primaryObj[username].projectCountByPhase,
      stack: "A",
      label: primaryObj[username].primaryName,
      color: palette[index]
    }

    let primaryProjectObj = {
      value: primaryObj[username].projectCount,
      label: primaryObj[username].primaryName
    }

    primaryPhaseData.push(primaryPhaseObj)
    primaryProjectData.push(primaryProjectObj)
  }

  let xAxisMonthArray = []
  let projecCountArray = []

  for (let key in createdDateObj) {
    let xLabel = `${createdDateObj[key].month} ${createdDateObj[key].year}`
    let currentProjectCount = createdDateObj[key].projectCount

    xAxisMonthArray.push(xLabel)
    projecCountArray.push(currentProjectCount)
  }

  let xAxisHashtagArray = []
  let hashtagCountArray = []

  for (let key in hashtagObj) {
    xAxisHashtagArray.push(key)
    hashtagCountArray.push(hashtagObj[key])
  }

  return {
    primaryPhaseData: primaryPhaseData,
    phaseData: phaseArrayYAxis,
    primaryProjectData: primaryProjectData,
    xAxisMonthArray: xAxisMonthArray,
    projectCountArray: projecCountArray,
    xAxisHashtagArray: xAxisHashtagArray,
    hashtagCountArray: hashtagCountArray
  }
})
