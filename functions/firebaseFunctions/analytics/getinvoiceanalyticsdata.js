import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getOrgInvoiceItems from "../../services/filevine/getOrgInvoiceItems.js"

export const getinvoiceanalyticsdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  // =========================================
  // GET PAST 12 MONTH ARRAY
  // =========================================

  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth()

  const oneYearAgo = currentYear - 1
  const monthAbbreviations = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ]

  let createdDateObj = {}

  for (let year = oneYearAgo; year <= currentYear; year++) {
    // Determine the range of months to iterate through based on the current year
    const startMonth = year === oneYearAgo ? currentMonth + 1 : 0
    const endMonth = year === currentYear ? currentMonth : 11

    // Loop through each month and compile the data
    for (let month = startMonth; month <= endMonth; month++) {
      const currentMonth = monthAbbreviations[month]
      const yearMonth = `${year}-${(month + 1).toString().padStart(2, "0")}` // Format as "YYYY-MM"

      // Generate some example data (can be replaced with actual data retrieval)
      const monthData = {
        month: currentMonth,
        year: year,
        amountDue: 0,
        amountPaid: 0
      }

      // Assign the data for the current month and year to the object
      createdDateObj[yearMonth] = monthData
    }
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  let invoiceItems = []
  let offset = 0
  let hasMore = true
  let startDate

  if (currentMonth < 2) {
    startDate = `${currentYear - 2}-11-01`
  } else {
    startDate = `${currentYear - 1}-${currentMonth - 1}-01`
  }

  while (hasMore) {
    await getOrgInvoiceItems(accessToken, refreshToken, fvData, 1000, offset, startDate)
      .then((data) => {
        invoiceItems.push(...data.items)
        hasMore = data.hasMore
        offset = offset + 1000
      })
      .catch((err) => {
        console.error(err, "error")
        hasMore = false
      })
  }

  for (let i = 0; i < invoiceItems.length; i++) {
    let invoiceMonth = invoiceItems[i].invoiceDate.slice(0, 7)

    if (createdDateObj[invoiceMonth]) {
      createdDateObj[invoiceMonth].amountDue += invoiceItems[i].total
      createdDateObj[invoiceMonth].amountPaid += invoiceItems[i].total - invoiceItems[i].outstandingBalance
    }
  }

  let xAxisMonthArray = []
  let amountDueArray = []
  let amountPaidArray = []

  for (let key in createdDateObj) {
    let xLabel = `${createdDateObj[key].month} ${createdDateObj[key].year}`

    xAxisMonthArray.push(xLabel)
    amountDueArray.push(createdDateObj[key].amountDue)
    amountPaidArray.push(createdDateObj[key].amountPaid)
  }

  return {
    xAxisHashtagArray: xAxisMonthArray,
    amountDueArray: amountDueArray,
    amountPaidArray: amountPaidArray
  }
})
