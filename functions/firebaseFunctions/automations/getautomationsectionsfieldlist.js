import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getSectionFieldList from "../../services/filevine/getSectionFieldList.js"
import { acceptedFields } from "../../constants/accepted-fields.js"

export const getautomationsectionsfieldlist = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const projectTypeId = request.data.projectTypeId.toString()
  const triggerSectionSelector = request.data.triggerSectionSelector
  const targetSectionSelector = request.data.targetSectionSelector

  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error"))
  }

  let actionButtonList = []
  let triggerValidFieldList = []
  const targetFieldTypeObject = {}

  await getSectionFieldList(accessToken, refreshToken, fvData, projectTypeId, triggerSectionSelector)
    .then((data) => {
      for (let i = 0; i < data.customFields.length; i++) {
        if (data.customFields[i].customFieldType === "ActionButton") {
          actionButtonList.push(data.customFields[i])
        } else {
          if (acceptedFields[data.customFields[i].customFieldType]) {
            triggerValidFieldList.push(data.customFields[i])
            targetFieldTypeObject[data.customFields[i].customFieldType] = []
          }
        }
      }
    })
    .catch((err) => console.error(err, "error getting trigger section field list"))

  await getSectionFieldList(accessToken, refreshToken, fvData, projectTypeId, targetSectionSelector)
    .then((data) => {
      for (let i = 0; i < data.customFields.length; i++) {
        if (targetFieldTypeObject[data.customFields[i].customFieldType]) {
          targetFieldTypeObject[data.customFields[i].customFieldType].push(data.customFields[i])
        }
      }
    })
    .catch((err) => console.error(err, "error getting target section field list"))

  return {
    actionButtonList: actionButtonList,
    triggerValidFieldList: triggerValidFieldList,
    targetFieldTypeObject: targetFieldTypeObject
  }
})
