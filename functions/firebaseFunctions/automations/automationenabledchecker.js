import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const automationenabledchecker = v2.https.onCall(async (request) => {
  const db = getFirestore()
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  const automationData = db.collection(fvData.tenant).doc("automations")

  const automationDoc = await automationData.get()

  if (!automationDoc.exists) {
    await db
      .collection(fvData.tenant)
      .doc("automations")
      .set({ enabled: false })
      .catch(() => console.error("error disabling automations"))
  } else {
    return automationDoc.data()
  }
})
