import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const getindidualautomationdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let triggerAutomations = {}

  const db = getFirestore()

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .collection("customTaskflowAutomations")
    .doc(request.data.triggerKey)
    .get()
    .then((docs) => {
      triggerAutomations = docs.data()
    })

  return {
    automationMap: triggerAutomations.createCollectionAutomations[request.data.targetSectionSelector]
  }
})
