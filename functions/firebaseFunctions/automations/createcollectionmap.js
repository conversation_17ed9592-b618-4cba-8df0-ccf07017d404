import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const createcollectionmap = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const db = getFirestore()

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let automationData
  let newAutomationData = request.data.automationData
  let dotNotation = `createCollectionAutomations.${newAutomationData.targetSectionSelector}`

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .collection("customTaskflowAutomations")
    .doc(request.data.triggerKey)
    .get()
    .then((data) => (automationData = data.data()))

  if (automationData) {
    let existingCreateCollectionAutomations = automationData.createCollectionAutomations

    if (existingCreateCollectionAutomations[newAutomationData.targetSectionSelector]) {
      let newFieldMap = { ...existingCreateCollectionAutomations.fieldMap, ...newAutomationData.fieldMap }
      newAutomationData.fieldMap = newFieldMap
    }

    await db
      .collection(fvData.tenant)
      .doc("automations")
      .collection("customTaskflowAutomations")
      .doc(request.data.triggerKey)
      .update({ [dotNotation]: newAutomationData })
  } else {
    await db
      .collection(fvData.tenant)
      .doc("automations")
      .collection("customTaskflowAutomations")
      .doc(request.data.triggerKey)
      .set({ createCollectionAutomations: { [newAutomationData.targetSectionSelector]: newAutomationData } })
  }

  return
})
