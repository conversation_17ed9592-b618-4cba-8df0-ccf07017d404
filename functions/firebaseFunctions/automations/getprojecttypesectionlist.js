import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getProjectTypeSectionList from "../../services/filevine/getProjectTypeSectionList.js"

export const getprojecttypesectionlist = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  const projectTypeId = request.data.projectTypeId.toString()
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let accessToken
  let refreshToken

  if (fvData.pat) {
    await createSessionPAT(fvData).then((data) => {
      accessToken = data.access_token
    })
  } else {
    await createSession(fvData)
      .then((data) => {
        accessToken = data.accessToken
        refreshToken = data.refreshToken
      })
      .catch((err) => console.error(err, "error creating session"))
  }

  let staticSectionList = []
  let collectionSectionList = []

  await getProjectTypeSectionList(accessToken, refreshToken, fvData, projectTypeId)
    .then((data) => {
      for (let i = 0; i < data.items.length; i++) {
        if (data.items[i].isCollection) {
          collectionSectionList.push(data.items[i])
        } else {
          staticSectionList.push(data.items[i])
        }
      }
    })
    .catch((err) => console.error(err, "error getting project type section list"))

  return {
    staticSectionList: staticSectionList,
    collectionSectionList: collectionSectionList
  }
})
