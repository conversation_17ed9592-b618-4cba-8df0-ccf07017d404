import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import createSubscription from "../../services/filevine/createSubscription.js"

export const automationswitchhandler = v2.https.onCall(async (request) => {
  const db = getFirestore()
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  const legalwareAutomationData = request.data.automationSwitchData
  let firebaseAutomationData

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .get()
    .then((data) => (firebaseAutomationData = data.data()))
    .catch(() => console.error("error getting firebase automation data"))

  if (request.data.automationSwitchData.enabled && !firebaseAutomationData.subscriptionId) {
    const subscriptionBody = {
      name: "Legalware",
      description: "Legalware Automation",
      endpoint: `https://legalwareautomation-l3laebfmma-uc.a.run.app/${fvData.tenant}`,
      eventIds: ["Project.Created", "Project.PhaseChanged", "Taskflow.Executed", "CollectionItem.Created"]
    }

    let accessToken
    let refreshToken

    if (fvData.pat) {
      await createSessionPAT(fvData).then((data) => {
        accessToken = data.access_token
      })
    } else {
      subscriptionBody.keyId = fvData.key

      await createSession(fvData)
        .then((data) => {
          accessToken = data.accessToken
          refreshToken = data.refreshToken
        })
        .catch((err) => console.error(err, "error creating session"))
    }

    let subscriptionId

    await createSubscription(accessToken, refreshToken, fvData, subscriptionBody)
      .then((data) => (subscriptionId = fvData.pat ? data.id : data.subscriptionId))
      .catch((err) => console.error(err, "error creating subscription"))

    legalwareAutomationData.subscriptionId = subscriptionId
  }

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .update(legalwareAutomationData)
    .catch(() => console.error("error enabling automations"))
})
