import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const getcustomautomationdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let automationTriggerArray = []

  const db = getFirestore()

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .collection("customTaskflowAutomations")
    .get()
    .then((docs) => {
      docs.forEach((doc) => {
        automationTriggerArray.push(doc.data())
      })
    })

  let customAutomationArray = []

  for (let i = 0; i < automationTriggerArray.length; i++) {
    let triggerList = automationTriggerArray[i].createCollectionAutomations

    for (let targetSection in triggerList) customAutomationArray.push(triggerList[targetSection])
  }

  return {
    customAutomationArray: customAutomationArray
  }
})
