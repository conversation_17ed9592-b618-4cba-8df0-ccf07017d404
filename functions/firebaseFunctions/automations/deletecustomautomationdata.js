import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const deletecustomautomationdata = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  let deletedAutomationTrigger = {}

  const db = getFirestore()

  await db
    .collection(fvData.tenant)
    .doc("automations")
    .collection("customTaskflowAutomations")
    .doc(request.data.triggerKey)
    .get()
    .then((docs) => {
      deletedAutomationTrigger = docs.data()
    })

  let triggerTypeSize = Object.keys(deletedAutomationTrigger).length
  let triggerTargetSize = Object.keys(deletedAutomationTrigger[request.data.automationType]).length

  if (triggerTypeSize === 1 && triggerTargetSize === 1) {
    await db
      .collection(fvData.tenant)
      .doc("automations")
      .collection("customTaskflowAutomations")
      .doc(request.data.triggerKey)
      .delete()
  } else {
    if (triggerTargetSize > 1) {
      delete deletedAutomationTrigger[request.data.automationType][request.data.targetSectionSelector]
    }
    if (triggerTargetSize === 1 && triggerTypeSize > 1) {
      delete deletedAutomationTrigger[request.data.automationType]
    }

    await db
      .collection(fvData.tenant)
      .doc("automations")
      .collection("customTaskflowAutomations")
      .doc(request.data.triggerKey)
      .set(deletedAutomationTrigger)
  }

  return
})
