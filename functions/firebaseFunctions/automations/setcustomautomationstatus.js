import * as v2 from "firebase-functions/v2"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"
import { getFirestore } from "firebase-admin/firestore"

export const setcustomautomationstatus = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }
  const db = getFirestore()

  const userEmail = request.auth.token.email
  let fvData = null

  await getCredsFromFirestore(userEmail)
    .then((data) => (fvData = data))
    .catch((error) => console.error(error, "error getting data from FS"))

  if (!fvData) {
    throw new v2.https.HttpsError("failed", "User not authorized in tenant.")
  }

  const switchedAutomations = request.data.switchedAutomations

  for (let i = 0; i < switchedAutomations.length; i++) {
    let dotNotation = `${switchedAutomations[i].automationType}.${switchedAutomations[i].targetSectionSelector}.enabled`

    await db
      .collection(fvData.tenant)
      .doc("automations")
      .collection(switchedAutomations[i].triggerType)
      .doc(switchedAutomations[i].triggerKey)
      .update({ [dotNotation]: switchedAutomations[i].enabled })
      .catch(() => console.error("error enabling automations"))
  }
})
