import * as v2 from "firebase-functions/v2"
import getProjectCollectionItemList from "../../services/filevine/getProjectCollectionItemList.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getRopsioCreds from "../../services/getRopsioCreds.js"
import { SUPPORT_TICKETS_SELECTOR } from "./supportTicketsConstants.js"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"

export const getSupportTickets = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const { email } = request.auth.token

  try {
    // Get credentials
    const ropsioFvData = await getRopsioCreds()
    const userTenantFvData = await getCredsFromFirestore(email)

    // Create session
    const { accessToken, refreshToken } = await createAuthSession(ropsioFvData)

    // Find support project for tenant
    const tenantProjectId = userTenantFvData.managedServiceProjectId

    // Get collection items
    const projectCollectionItemList = await getProjectCollectionItemList(
      accessToken,
      refreshToken,
      ropsioFvData,
      tenantProjectId,
      SUPPORT_TICKETS_SELECTOR
    )

    return {
      projectCollectionItemList: projectCollectionItemList.items
    }
  } catch (error) {
    console.error("Error in getSupportTickets:", error)
    throw new v2.https.HttpsError("internal", error.message)
  }
})

async function createAuthSession(fvData) {
  if (fvData.pat) {
    const { access_token } = await createSessionPAT(fvData)
    return { accessToken: access_token, refreshToken: null }
  }

  const { accessToken, refreshToken } = await createSession(fvData)
  return { accessToken, refreshToken }
}
