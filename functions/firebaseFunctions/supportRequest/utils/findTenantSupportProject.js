import { TENANT_TO_PROJECT_MAP, SUPPORT_PROJECT_TYPE_ID } from "../supportTicketsConstants.js"

function findTenantSupportProject(projectList, tenant) {
  const supportProjects = projectList.filter(
    (project) => project.projectTypeId.native === SUPPORT_PROJECT_TYPE_ID
  )

  const tenantProjectId = TENANT_TO_PROJECT_MAP[tenant]
  const tenantProject = supportProjects.find((project) => project.projectId.native === tenantProjectId)

  if (!tenantProject) {
    throw new Error(`Support project not found for tenant: ${tenant}`)
  }

  return tenantProject.projectId.native
}

export default findTenantSupportProject
