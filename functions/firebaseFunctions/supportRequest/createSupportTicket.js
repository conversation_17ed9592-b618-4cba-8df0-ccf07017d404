import * as v2 from "firebase-functions/v2"
import createCollectionItem from "../../services/filevine/createCollectionItem.js"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getRopsioCreds from "../../services/getRopsioCreds.js"
import createNote from "../../services/filevine/createNote.js"
import { SUPPORT_TICKETS_SELECTOR } from "./supportTicketsConstants.js"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"

export const createSupportTicket = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const { email } = request.auth.token
  const { dataobject } = request.data

  console.log(dataobject, "data object")

  let body = { dataobject }

  try {
    // Get credentials
    const ropsioFvData = await getRopsioCreds()
    const userTenantFvData = await getCredsFromFirestore(email)

    console.log(userTenantFvData, "utfd")

    // Create session
    const { accessToken, refreshToken } = await createAuthSession(ropsioFvData)

    // Find support project for tenant
    const tenantProjectId = userTenantFvData.managedServiceProjectId

    const collectionItemResponse = await createCollectionItem(
      accessToken,
      refreshToken,
      ropsioFvData,
      tenantProjectId,
      SUPPORT_TICKETS_SELECTOR,
      body
    )

    const taskBody = {
      body: `@${body}`,
      projectId: {
        native: userTenantFvData.managedServiceProjectId
      }
    }

    return collectionItemResponse
  } catch (error) {
    console.error("Error in getSupportTickets:", error)
    throw new v2.https.HttpsError("internal", error.message)
  }
})

async function createAuthSession(fvData) {
  if (fvData.pat) {
    const { access_token } = await createSessionPAT(fvData)
    return { accessToken: access_token, refreshToken: null }
  }

  const { accessToken, refreshToken } = await createSession(fvData)
  return { accessToken, refreshToken }
}