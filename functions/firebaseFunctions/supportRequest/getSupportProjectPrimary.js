import * as v2 from "firebase-functions/v2"
import createSession from "../../services/filevine/createSession.js"
import createSessionPAT from "../../services/filevine/createSessionPAT.js"
import getRopsioCreds from "../../services/getRopsioCreds.js"
import getContacts from "../../services/filevine/getContacts.js"
import getProject from "../../services/filevine/getProject.js"
import getCredsFromFirestore from "../../services/getCredsFromFirestore.js"

export const getSupportProjectPrimary = v2.https.onCall(async (request) => {
  if (!request.auth) {
    throw new v2.https.HttpsError("failed-precondition", "The function must be called while authenticated.")
  }

  const { email } = request.auth.token

  try {
    // Get credentials
    const ropsioFvData = await getRopsioCreds()
    const userTenantFvData = await getCredsFromFirestore(email)

    // Create session
    const { accessToken, refreshToken } = await createAuthSession(ropsioFvData)

    // Find support project for tenant
    const tenantProjectData = await getProject(accessToken, refreshToken, ropsioFvData, userTenantFvData.managedServiceProjectId)

    console.log(tenantProjectData, "tpd")

    const teamList = []
    let hasMore = true
    let offset = 0

    while (hasMore) {
      await getContacts(accessToken, refreshToken, ropsioFvData, 1000, offset)
        .then((data) => {
          teamList.push(...data.items)
          hasMore = data.hasMore
          offset = offset + 1000
        })
        .catch((err) => {
          hasMore = false
          throw new Error("failed", "Error getting contact list", err)
        })
    }

    const primary = teamList.filter((team) => team.fullName === tenantProjectData.firstPrimaryName)

    return primary[0]
  } catch (error) {
    console.error("Error in getSupportTickets:", error)
    throw new v2.https.HttpsError("internal", error.message)
  }
})

async function createAuthSession(fvData) {
  if (fvData.pat) {
    const { access_token } = await createSessionPAT(fvData)
    return { accessToken: access_token, refreshToken: null }
  }

  const { accessToken, refreshToken } = await createSession(fvData)
  return { accessToken, refreshToken }
}