// hash.js
const crypto = require("crypto")
const fs = require("fs")

// You need to specify what file or data you want to hash
// For example, to hash package.json:

const timestamp = new Date().toISOString()
const data = [
  "fvpk_e8678054-30aa-2a58-34f2-6890bdd30d48",
  timestamp,
  "fvsk_52232014c8942712cbaef5196428c38197f7ee994e693c2f454f982bf45f4cf9733ba9e5129440283a23637a5117aa68"
].join("/")
try {
  const hash = crypto.createHash("md5").update(data).digest("hex")
  console.log(hash, timestamp)
} catch (err) {
  console.error("Error generating hash:", err)
}
